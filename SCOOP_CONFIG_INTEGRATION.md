# Scoop 配置文件集成

## 配置文件位置

按照 Scoop 的标准，配置文件位于：
```
%USERPROFILE%\.config\scoop\config.json
```

例如：`C:\Users\<USER>\.config\scoop\config.json`

## 配置文件格式

### 示例配置文件
```json
{
  "root_path": "C:\\Users\\<USER>\\scoop",
  "aria2-enabled": true,
  "aria2-warning-enabled": true,
  "aria2-retry-wait": 2,
  "aria2-split": 5,
  "aria2-max-connection-per-server": 5,
  "aria2-min-split-size": "5M",
  "aria2-options": "",
  "debug": false,
  "force_update": false,
  "show_update_log": true,
  "scoop_repo": "https://github.com/ScoopInstaller/Scoop",
  "scoop_branch": "master"
}
```

## 关键配置项

### root_path
- **用途**: 指定 Scoop 的安装根目录
- **默认值**: `%USERPROFILE%\scoop` (用户模式) 或 `%PROGRAMDATA%\scoop` (全局模式)
- **影响的命令**:
  - `sco list` - 从 `{root_path}\apps` 查找已安装应用
  - `sco install` - 安装应用到 `{root_path}\apps\{app_name}`
  - `sco status` - 检查 `{root_path}` 下的状态
  - `sco cache` - 使用 `{root_path}\cache` 作为缓存目录

## 目录结构

基于 `root_path` 的目录结构：
```
{root_path}/
├── apps/           # 已安装的应用
│   ├── app1/
│   ├── app2/
│   └── ...
├── buckets/        # Bucket 源
├── cache/          # 下载缓存
├── shims/          # 可执行文件 shims
└── persist/        # 持久化数据
```

## 实现的功能

### ✅ 配置文件读取
```cpp
// 自动读取 %USERPROFILE%\.config\scoop\config.json
auto& config = Config::instance();
config.load();

// 获取 root_path
std::string root_path = config.get("root_path");
std::filesystem::path scoop_dir = config.get_root_path();
```

### ✅ 路径解析
```cpp
// 所有路径都基于配置的 root_path
auto apps_dir = config.get_apps_dir();      // {root_path}/apps
auto cache_dir = config.get_cache_dir();    // {root_path}/cache
auto buckets_dir = config.get_buckets_dir(); // {root_path}/buckets
auto shims_dir = config.get_shims_dir();    // {root_path}/shims
```

### ✅ 配置管理命令
```bash
# 查看所有配置
sco config

# 查看 root_path
sco config root_path

# 设置 root_path
sco config root_path "D:\MyScoop"
```

### ✅ 自动配置创建
- 如果配置文件不存在，自动创建默认配置
- 如果 `root_path` 未设置，使用默认路径
- 自动创建必要的目录结构

## 命令行为变化

### list 命令
```bash
# 现在从配置的 root_path 读取应用列表
sco list
# 扫描: {root_path}/apps/ 目录
```

### install 命令 (未来实现)
```bash
# 安装到配置的 root_path
sco install git
# 安装到: {root_path}/apps/git/
```

### status 命令
```bash
# 显示基于配置 root_path 的状态
sco status
# 检查: {root_path}/ 下的各种状态
```

## 配置验证

### root_path 验证
- 必须是绝对路径
- 自动创建目录（如果不存在）
- 验证写入权限

```cpp
// 设置 root_path 时的验证
sco config root_path "D:\MyScoop"
// 验证: 路径是否绝对，是否可创建，是否有写权限
```

## 兼容性

### 与原版 Scoop 兼容
- ✅ 使用相同的配置文件位置
- ✅ 使用相同的配置格式
- ✅ 支持相同的配置项
- ✅ 兼容现有的目录结构

### 迁移支持
- 自动检测现有 Scoop 安装
- 读取现有配置文件
- 无缝切换到 C++ 版本

## 使用示例

### 初次使用
```bash
# 1. 运行任何命令，自动创建默认配置
sco config

# 2. 查看当前配置
sco config root_path
# 输出: C:\Users\<USER>\scoop

# 3. 自定义安装路径
sco config root_path "D:\MyApps\scoop"

# 4. 验证配置
sco config
```

### 现有 Scoop 用户
```bash
# 如果已有 Scoop 配置，直接使用
sco list
# 自动读取现有配置，显示已安装应用

# 查看当前配置
sco config
# 显示现有的 root_path 和其他配置
```

## 调试信息

使用 `--verbose` 查看配置加载过程：
```bash
sco --verbose config
# 显示:
# - 配置文件路径
# - 加载状态
# - 默认值设置
# - 路径解析过程
```

## 错误处理

### 配置文件损坏
- 自动备份原文件
- 重置为默认配置
- 记录错误日志

### 路径权限问题
- 检测权限问题
- 提供修复建议
- 支持权限提升

### 目录不存在
- 自动创建必要目录
- 验证创建成功
- 处理创建失败情况

这个实现确保了与原版 Scoop 的完全兼容性，同时提供了更好的错误处理和用户体验。
