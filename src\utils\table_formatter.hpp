#pragma once

#include <vector>
#include <string>
#include <iostream>
#include <iomanip>
#include <algorithm>

namespace sco {

class TableFormatter {
public:
    struct Column {
        std::string header;
        int width;
        bool left_align;

        Column(const std::string& h, bool left = true)
            : header(h), width(0), left_align(left) {}
    };
    
    using Row = std::vector<std::string>;
    
    TableFormatter() = default;
    
    // Add column definition
    void add_column(const std::string& header, bool left_align = true) {
        columns_.emplace_back(header, left_align);
    }
    
    // Add data row
    void add_row(const Row& row) {
        rows_.push_back(row);
    }
    
    // Print the table
    void print() {
        if (columns_.empty()) return;

        // Auto-adjust widths before printing
        auto_adjust_widths();

        // Print header
        print_separator();
        print_row_data(get_headers());
        print_separator();

        // Print data rows
        for (const auto& row : rows_) {
            print_row_data(row);
        }
    }
    
    // Print with custom separator
    void print_with_separator(char sep = '-') {
        if (columns_.empty()) return;

        // Auto-adjust widths before printing
        auto_adjust_widths();

        // Print header
        print_custom_separator(sep);
        print_row_data(get_headers());
        print_custom_separator(sep);

        // Print data rows
        for (const auto& row : rows_) {
            print_row_data(row);
        }
    }
    
    // Clear all data but keep column definitions
    void clear_rows() {
        rows_.clear();
    }
    
    // Clear everything
    void clear() {
        columns_.clear();
        rows_.clear();
    }
    


private:
    std::vector<Column> columns_;
    std::vector<Row> rows_;

    // Auto-adjust all column widths based on content
    void auto_adjust_widths() {
        // Reset widths to header length
        for (auto& col : columns_) {
            col.width = static_cast<int>(col.header.length());
        }

        // Adjust based on data
        for (const auto& row : rows_) {
            for (size_t i = 0; i < row.size() && i < columns_.size(); ++i) {
                columns_[i].width = std::max(columns_[i].width,
                    static_cast<int>(row[i].length()));
            }
        }
    }

    Row get_headers() const {
        Row headers;
        for (const auto& col : columns_) {
            headers.push_back(col.header);
        }
        return headers;
    }
    
    void print_row_data(const Row& row) const {
        for (size_t i = 0; i < columns_.size(); ++i) {
            if (i > 0) std::cout << " ";
            
            std::string cell_data = (i < row.size()) ? row[i] : "";
            
            if (columns_[i].left_align) {
                std::cout << std::left << std::setw(columns_[i].width) << cell_data;
            } else {
                std::cout << std::right << std::setw(columns_[i].width) << cell_data;
            }
        }
        std::cout << "\n";
    }
    
    void print_separator() const {
        for (size_t i = 0; i < columns_.size(); ++i) {
            if (i > 0) std::cout << " ";
            std::cout << std::string(columns_[i].width, '-');
        }
        std::cout << "\n";
    }
    
    void print_custom_separator(char sep) const {
        for (size_t i = 0; i < columns_.size(); ++i) {
            if (i > 0) std::cout << " ";
            std::cout << std::string(columns_[i].width, sep);
        }
        std::cout << "\n";
    }
};

} // namespace sco
