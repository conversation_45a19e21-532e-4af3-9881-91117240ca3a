#pragma once

#include <string>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <functional>
#include <spdlog/spdlog.h>

#ifdef _WIN32
#include <windows.h>
#include <wininet.h>
#pragma comment(lib, "wininet.lib")
#endif

namespace sco {

struct DownloadProgress {
    size_t total_bytes = 0;
    size_t downloaded_bytes = 0;
    double speed_bps = 0.0;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point last_update;
    
    double get_progress_percent() const {
        if (total_bytes == 0) return 0.0;
        return (static_cast<double>(downloaded_bytes) / total_bytes) * 100.0;
    }
    
    std::string get_speed_string() const {
        if (speed_bps < 1024) {
            return std::to_string(static_cast<int>(speed_bps)) + " B/s";
        } else if (speed_bps < 1024 * 1024) {
            return std::to_string(static_cast<int>(speed_bps / 1024)) + " KB/s";
        } else {
            return std::to_string(static_cast<int>(speed_bps / (1024 * 1024))) + " MB/s";
        }
    }
    
    std::string get_size_string(size_t bytes) const {
        if (bytes < 1024) {
            return std::to_string(bytes) + " B";
        } else if (bytes < 1024 * 1024) {
            return std::to_string(bytes / 1024) + " KB";
        } else {
            return std::to_string(bytes / (1024 * 1024)) + " MB";
        }
    }
};

class DownloadManager {
public:
    using ProgressCallback = std::function<void(const DownloadProgress&)>;
    
    struct DownloadResult {
        bool success = false;
        std::string error_message;
        std::filesystem::path downloaded_file;
        size_t total_bytes = 0;
        std::chrono::milliseconds duration{0};
    };
    
    static DownloadResult download_file(const std::string& url, 
                                      const std::filesystem::path& output_path,
                                      ProgressCallback progress_callback = nullptr) {
        DownloadResult result;
        result.downloaded_file = output_path;
        
        SPDLOG_INFO("Downloading: {} -> {}", url, output_path.string());
        
        try {
            // Ensure output directory exists
            std::filesystem::create_directories(output_path.parent_path());
            
#ifdef _WIN32
            return download_with_wininet(url, output_path, progress_callback);
#else
            return download_with_curl(url, output_path, progress_callback);
#endif
        } catch (const std::exception& e) {
            result.success = false;
            result.error_message = e.what();
            SPDLOG_ERROR("Download failed: {}", e.what());
            return result;
        }
    }
    
    static bool verify_hash(const std::filesystem::path& file_path, const std::string& expected_hash) {
        if (expected_hash.empty()) {
            SPDLOG_DEBUG("No hash provided for verification");
            return true; // No hash to verify
        }

        // TODO: Implement proper SHA256 verification
        SPDLOG_WARN("Hash verification temporarily disabled - file: {}", file_path.string());
        SPDLOG_DEBUG("Expected hash: {}", expected_hash);
        return true; // Temporarily skip hash verification

        try {
            std::string actual_hash = calculate_sha256(file_path);
            bool matches = (actual_hash == expected_hash);

            if (matches) {
                SPDLOG_DEBUG("Hash verification successful for: {}", file_path.string());
            } else {
                SPDLOG_ERROR("Hash verification failed for: {}", file_path.string());
                SPDLOG_ERROR("Expected: {}", expected_hash);
                SPDLOG_ERROR("Actual:   {}", actual_hash);
            }

            return matches;
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Hash calculation failed: {}", e.what());
            return false;
        }
    }
    
private:
#ifdef _WIN32
    static DownloadResult download_with_wininet(const std::string& url, 
                                               const std::filesystem::path& output_path,
                                               ProgressCallback progress_callback) {
        DownloadResult result;
        auto start_time = std::chrono::steady_clock::now();
        
        HINTERNET hInternet = InternetOpenA("sco/1.0", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
        if (!hInternet) {
            result.error_message = "Failed to initialize WinINet";
            return result;
        }
        
        HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, 
                                         INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        if (!hUrl) {
            InternetCloseHandle(hInternet);
            result.error_message = "Failed to open URL";
            return result;
        }
        
        // Get content length
        DWORD content_length = 0;
        DWORD buffer_size = sizeof(content_length);
        DWORD index = 0;
        
        HttpQueryInfoA(hUrl, HTTP_QUERY_CONTENT_LENGTH | HTTP_QUERY_FLAG_NUMBER,
                      &content_length, &buffer_size, &index);
        
        std::ofstream output_file(output_path, std::ios::binary);
        if (!output_file.is_open()) {
            InternetCloseHandle(hUrl);
            InternetCloseHandle(hInternet);
            result.error_message = "Failed to create output file";
            return result;
        }
        
        DownloadProgress progress;
        progress.total_bytes = content_length;
        progress.start_time = start_time;
        progress.last_update = start_time;
        
        const DWORD buffer_size_read = 8192;
        char buffer[buffer_size_read];
        DWORD bytes_read;
        
        while (InternetReadFile(hUrl, buffer, buffer_size_read, &bytes_read) && bytes_read > 0) {
            output_file.write(buffer, bytes_read);
            progress.downloaded_bytes += bytes_read;
            
            // Update progress
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - progress.last_update);
            
            if (elapsed.count() >= 100 || progress.downloaded_bytes == progress.total_bytes) { // Update every 100ms
                auto total_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - progress.start_time);
                if (total_elapsed.count() > 0) {
                    progress.speed_bps = (static_cast<double>(progress.downloaded_bytes) * 1000.0) / total_elapsed.count();
                }
                progress.last_update = now;
                
                if (progress_callback) {
                    progress_callback(progress);
                }
            }
        }
        
        output_file.close();
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        
        auto end_time = std::chrono::steady_clock::now();
        result.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        result.total_bytes = progress.downloaded_bytes;
        result.success = true;
        
        SPDLOG_INFO("Download completed: {} bytes in {}ms", 
                   result.total_bytes, result.duration.count());
        
        return result;
    }
#else
    static DownloadResult download_with_curl(const std::string& url, 
                                           const std::filesystem::path& output_path,
                                           ProgressCallback progress_callback) {
        DownloadResult result;
        result.error_message = "cURL download not implemented yet";
        return result;
    }
#endif
    
    static std::string calculate_sha256(const std::filesystem::path& file_path) {
        // For now, return empty string - proper SHA256 implementation would require
        // additional dependencies like OpenSSL or Windows CryptoAPI
        SPDLOG_WARN("SHA256 calculation not implemented yet");
        return "";
    }
    
public:
    // Utility function to show progress bar
    static void show_progress_bar(const DownloadProgress& progress) {
        const int bar_width = 50;
        double percent = progress.get_progress_percent();
        int filled = static_cast<int>(percent * bar_width / 100.0);
        
        std::cout << "\r[";
        for (int i = 0; i < bar_width; ++i) {
            if (i < filled) {
                std::cout << "=";
            } else if (i == filled) {
                std::cout << ">";
            } else {
                std::cout << " ";
            }
        }
        std::cout << "] " << std::fixed << std::setprecision(1) << percent << "% ";
        
        if (progress.total_bytes > 0) {
            std::cout << progress.get_size_string(progress.downloaded_bytes) 
                     << "/" << progress.get_size_string(progress.total_bytes) << " ";
        }
        
        std::cout << progress.get_speed_string();
        std::cout.flush();
        
        if (progress.downloaded_bytes == progress.total_bytes && progress.total_bytes > 0) {
            std::cout << std::endl;
        }
    }
    
    // Download with default progress display
    static DownloadResult download_with_progress(const std::string& url, 
                                                const std::filesystem::path& output_path) {
        return download_file(url, output_path, [](const DownloadProgress& progress) {
            show_progress_bar(progress);
        });
    }
    
    // Check if URL is accessible
    static bool check_url_accessible(const std::string& url) {
#ifdef _WIN32
        HINTERNET hInternet = InternetOpenA("sco/1.0", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
        if (!hInternet) {
            return false;
        }
        
        HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, 
                                         INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        bool accessible = (hUrl != NULL);
        
        if (hUrl) InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        
        return accessible;
#else
        return false; // Not implemented for non-Windows
#endif
    }
    
    // Get file size from URL without downloading
    static size_t get_remote_file_size(const std::string& url) {
#ifdef _WIN32
        HINTERNET hInternet = InternetOpenA("sco/1.0", INTERNET_OPEN_TYPE_PRECONFIG, NULL, NULL, 0);
        if (!hInternet) {
            return 0;
        }
        
        HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, 
                                         INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE, 0);
        if (!hUrl) {
            InternetCloseHandle(hInternet);
            return 0;
        }
        
        DWORD content_length = 0;
        DWORD buffer_size = sizeof(content_length);
        DWORD index = 0;
        
        HttpQueryInfoA(hUrl, HTTP_QUERY_CONTENT_LENGTH | HTTP_QUERY_FLAG_NUMBER,
                      &content_length, &buffer_size, &index);
        
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        
        return content_length;
#else
        return 0; // Not implemented for non-Windows
#endif
    }
};

} // namespace sco
