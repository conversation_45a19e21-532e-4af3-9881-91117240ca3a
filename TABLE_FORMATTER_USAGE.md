# 表格格式化工具使用指南

## 问题解决

### 🚫 制表符对齐的问题
使用制表符 (`\t`) 进行表格对齐存在以下问题：
- 制表符宽度不固定（通常是8个字符，但可能变化）
- 长文本会破坏对齐
- 不同终端显示效果不一致
- 无法精确控制列宽

### ✅ 新的解决方案
创建了 `TableFormatter` 类，提供：
- 精确的列宽控制
- 自动内容宽度计算
- 完美的对齐效果
- 可重用的通用组件

## TableFormatter 类功能

### 核心特性
```cpp
class TableFormatter {
public:
    // 添加列定义
    void add_column(const std::string& header, int width = 0, bool left_align = true);
    
    // 添加数据行
    void add_row(const Row& row);
    
    // 打印表格
    void print() const;
    
    // 自动调整列宽
    void auto_adjust_widths();
    
    // 清除数据
    void clear_rows();
    void clear();
};
```

### 使用示例

#### 基本用法
```cpp
#include "../utils/table_formatter.hpp"

TableFormatter table;

// 定义列
table.add_column("Name", 20, true);      // 最小宽度20，左对齐
table.add_column("Version", 15, true);   // 最小宽度15，左对齐
table.add_column("Size", 10, false);     // 最小宽度10，右对齐

// 添加数据
table.add_row({"git", "2.42.0", "45.2MB"});
table.add_row({"very-long-application-name", "1.0.0", "1.2GB"});
table.add_row({"vim", "9.0", "15MB"});

// 自动调整宽度并打印
table.auto_adjust_widths();
table.print();
```

#### 输出效果
```
Name                         Version         Size
---------------------------- --------------- ----------
git                          2.42.0               45.2MB
very-long-application-name   1.0.0                 1.2GB
vim                          9.0                    15MB
```

## 在 List 命令中的应用

### 之前的问题
```cpp
// 制表符方式 - 对齐不准确
std::cout << app.name << "\t\t\t" << app.version << "\t\t" << app.source << "\n";
```

### 现在的解决方案
```cpp
void print_apps_table(const std::vector<AppInfo>& apps) {
    std::cout << "Installed apps:\n\n";
    
    // 创建表格格式化器
    TableFormatter table;
    
    // 定义列
    table.add_column("Name", 20, true);
    table.add_column("Version", 15, true);
    table.add_column("Source", 8, true);
    table.add_column("Updated", 19, true);
    table.add_column("Info", 10, true);
    
    // 添加数据
    for (const auto& app : apps) {
        table.add_row({
            app.name,
            app.version,
            app.source,
            app.updated,
            app.info
        });
    }
    
    // 自动调整并打印
    table.auto_adjust_widths();
    table.print();
}
```

### 输出效果
```
Installed apps:

Name                         Version         Source   Updated             Info
---------------------------- --------------- -------- ------------------- ----------
7zip                         24.09           main     2025-06-20 20:26:47
everything                   1.4.1.1027      extras   2025-06-20 20:50:55
very-long-application-name   1.0.0           spc      2025-06-21 10:30:00
vscode                       1.101.1         extras   2025-06-21 22:18:20
```

## 在其他命令中的应用

### Status 命令
```cpp
void show_installed_apps_status() {
    TableFormatter table;
    table.add_column("Name", 20, true);
    table.add_column("Version", 15, true);
    table.add_column("Status", 12, true);
    
    // 添加应用状态数据
    for (const auto& app : apps) {
        std::string status = is_outdated(app) ? "Update available" : "Up to date";
        table.add_row({app.name, app.version, status});
    }
    
    table.auto_adjust_widths();
    table.print();
}
```

### Cache 命令
```cpp
void show_cache_contents() {
    TableFormatter table;
    table.add_column("App", 20, true);
    table.add_column("File", 30, true);
    table.add_column("Size", 10, false);  // 右对齐
    table.add_column("Date", 19, true);
    
    // 添加缓存文件信息
    for (const auto& file : cache_files) {
        table.add_row({
            file.app_name,
            file.filename,
            format_bytes(file.size),
            format_time(file.date)
        });
    }
    
    table.auto_adjust_widths();
    table.print();
}
```

### Search 命令 (未来实现)
```cpp
void show_search_results() {
    TableFormatter table;
    table.add_column("Name", 25, true);
    table.add_column("Version", 12, true);
    table.add_column("Source", 10, true);
    table.add_column("Description", 40, true);
    
    // 添加搜索结果
    for (const auto& result : search_results) {
        table.add_row({
            result.name,
            result.version,
            result.bucket,
            result.description
        });
    }
    
    table.auto_adjust_widths();
    table.print();
}
```

## 高级功能

### 自定义分隔符
```cpp
// 使用等号作为分隔符
table.print_with_separator('=');
```

输出：
```
Name                         Version         Source
============================ =============== ========
git                          2.42.0          main
vim                          9.0             main
```

### 动态列宽调整
```cpp
// 设置最小列宽
table.set_column_width(0, 30);  // 第一列最小30字符

// 自动调整所有列宽
table.auto_adjust_widths();
```

### 数据重用
```cpp
TableFormatter table;
table.add_column("Name", 20, true);
table.add_column("Count", 8, false);

// 第一组数据
table.add_row({"Apps", "25"});
table.add_row({"Buckets", "3"});
table.print();

// 清除数据，重用表格结构
table.clear_rows();

// 第二组数据
table.add_row({"Cache Files", "150"});
table.add_row({"Total Size", "2.5GB"});
table.print();
```

## 优势总结

### 🎯 精确对齐
- 基于字符宽度的精确计算
- 支持任意长度的内容
- 完美的列对齐效果

### 🔧 灵活配置
- 可配置列宽、对齐方式
- 支持自动宽度调整
- 可自定义分隔符样式

### 🚀 高性能
- 一次计算，多次使用
- 最小化字符串操作
- 高效的内存使用

### 🔄 可重用
- 通用的表格组件
- 适用于所有命令
- 一致的显示风格

### 📱 兼容性
- 适用于所有终端
- 不依赖制表符设置
- 跨平台一致显示

## 最佳实践

### 1. 列宽设置
```cpp
// 为常见内容设置合理的最小宽度
table.add_column("Name", 20, true);        // 应用名称
table.add_column("Version", 15, true);     // 版本号
table.add_column("Updated", 19, true);     // 时间戳 (YYYY-MM-DD HH:MM:SS)
```

### 2. 对齐方式
```cpp
// 文本内容 - 左对齐
table.add_column("Name", 20, true);
table.add_column("Description", 40, true);

// 数字内容 - 右对齐
table.add_column("Size", 10, false);
table.add_column("Count", 8, false);
```

### 3. 自动调整
```cpp
// 总是在打印前调用自动调整
table.auto_adjust_widths();
table.print();
```

这个表格格式化工具解决了制表符对齐的所有问题，为所有命令提供了一致、美观的表格输出！
