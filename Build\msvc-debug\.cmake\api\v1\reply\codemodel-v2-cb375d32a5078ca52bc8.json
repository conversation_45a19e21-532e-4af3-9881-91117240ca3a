{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-99643011f22e320e88ac.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "sco", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "sco::@6890427a1f51a3e7e1df", "jsonFile": "target-sco-Debug-516a4d69ae0a64dcd153.json", "name": "sco", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Code/scoop/Build/msvc-debug", "source": "C:/Code/scoop"}, "version": {"major": 2, "minor": 7}}