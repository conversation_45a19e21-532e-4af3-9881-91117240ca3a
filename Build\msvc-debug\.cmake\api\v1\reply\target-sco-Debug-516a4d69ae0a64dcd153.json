{"artifacts": [{"path": "sco.exe"}, {"path": "sco.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "install", "target_link_libraries", "set_target_properties", "include", "_find_package", "find_package", "target_include_directories"], "files": ["C:/Local/vcpkg-latest/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt", "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfigTargets.cmake", "C:/Local/vcpkg-latest/installed/x64-windows/share/spdlog/spdlogConfig.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 20, "parent": 0}, {"command": 0, "file": 0, "line": 598, "parent": 1}, {"command": 2, "file": 1, "line": 44, "parent": 0}, {"command": 3, "file": 1, "line": 29, "parent": 0}, {"command": 7, "file": 1, "line": 14, "parent": 0}, {"command": 6, "file": 0, "line": 893, "parent": 5}, {"file": 3, "parent": 6}, {"command": 5, "file": 3, "line": 42, "parent": 7}, {"file": 2, "parent": 8}, {"command": 4, "file": 2, "line": 70, "parent": 9}, {"command": 8, "file": 1, "line": 23, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 4, "fragment": "/Zc:__cplusplus"}, {"backtrace": 4, "fragment": "/utf-8"}], "defines": [{"backtrace": 4, "define": "CLI11_COMPILE"}, {"backtrace": 4, "define": "FMT_SHARED"}, {"backtrace": 4, "define": "SPDLOG_FMT_EXTERNAL"}, {"backtrace": 4, "define": "SPDLOG_FWRITE_UNLOCKED"}], "includes": [{"backtrace": 11, "path": "C:/Code/scoop/src"}, {"backtrace": 11, "path": "C:/Code/scoop/Build/msvc-debug"}, {"backtrace": 4, "isSystem": true, "path": "C:/Local/vcpkg-latest/installed/x64-windows/include"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [0]}], "id": "sco::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 3, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/sco"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 4, "fragment": "C:\\Local\\vcpkg-latest\\installed\\x64-windows\\debug\\lib\\CLI11.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\Local\\vcpkg-latest\\installed\\x64-windows\\debug\\lib\\fmtd.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "sco", "nameOnDisk": "sco.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "", "sourceIndexes": [1]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "C:/Local/vcpkg-latest/installed/x64-windows/share/nlohmann_json/nlohmann_json.natvis", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}