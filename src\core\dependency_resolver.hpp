#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <queue>
#include <algorithm>
#include <spdlog/spdlog.h>
#include "manifest.hpp"

namespace sco {

struct DependencyNode {
    std::string name;
    std::string bucket;
    Manifest manifest;
    std::vector<std::string> dependencies;
    bool visited = false;
    bool in_stack = false;
    int depth = 0;
};

class DependencyResolver {
public:
    struct ResolveResult {
        std::vector<std::string> install_order;
        std::vector<std::string> circular_dependencies;
        std::vector<std::string> missing_dependencies;
        bool success = false;
    };
    
    static ResolveResult resolve(const std::vector<std::string>& app_names) {
        DependencyResolver resolver;
        return resolver.resolve_dependencies(app_names);
    }
    
private:
    std::unordered_map<std::string, DependencyNode> nodes_;
    std::unordered_set<std::string> visited_;
    std::unordered_set<std::string> in_stack_;
    std::vector<std::string> install_order_;
    std::vector<std::string> circular_deps_;
    std::vector<std::string> missing_deps_;
    
    ResolveResult resolve_dependencies(const std::vector<std::string>& app_names) {
        ResolveResult result;
        
        // Build dependency graph
        for (const auto& app_name : app_names) {
            if (!build_dependency_graph(app_name)) {
                SPDLOG_ERROR("Failed to build dependency graph for: {}", app_name);
                result.success = false;
                result.missing_dependencies = missing_deps_;
                return result;
            }
        }
        
        // Perform topological sort with cycle detection
        for (const auto& app_name : app_names) {
            if (visited_.find(app_name) == visited_.end()) {
                if (!topological_sort(app_name)) {
                    SPDLOG_ERROR("Circular dependency detected involving: {}", app_name);
                    result.success = false;
                    result.circular_dependencies = circular_deps_;
                    return result;
                }
            }
        }
        
        // Reverse the order (topological sort gives reverse dependency order)
        std::reverse(install_order_.begin(), install_order_.end());
        
        result.install_order = install_order_;
        result.success = true;
        return result;
    }
    
    bool build_dependency_graph(const std::string& app_name) {
        // Check if already processed
        if (nodes_.find(app_name) != nodes_.end()) {
            return true;
        }
        
        // Find and parse manifest
        auto manifest = ManifestParser::find_and_parse(app_name);
        if (!manifest.is_valid()) {
            SPDLOG_ERROR("Could not find manifest for: {}", app_name);
            missing_deps_.push_back(app_name);
            return false;
        }
        
        // Create node
        DependencyNode node;
        node.name = app_name;
        node.bucket = manifest.bucket;
        node.manifest = manifest;
        node.dependencies = manifest.depends;
        
        nodes_[app_name] = node;
        
        // Recursively build graph for dependencies
        for (const auto& dep : manifest.depends) {
            std::string dep_name = extract_app_name(dep);
            if (!build_dependency_graph(dep_name)) {
                return false;
            }
        }
        
        return true;
    }
    
    bool topological_sort(const std::string& app_name) {
        // Mark as visited and in current stack
        visited_.insert(app_name);
        in_stack_.insert(app_name);
        
        auto& node = nodes_[app_name];
        
        // Visit all dependencies first
        for (const auto& dep : node.dependencies) {
            std::string dep_name = extract_app_name(dep);
            
            // If dependency is in current stack, we have a cycle
            if (in_stack_.find(dep_name) != in_stack_.end()) {
                SPDLOG_ERROR("Circular dependency detected: {} -> {}", app_name, dep_name);
                circular_deps_.push_back(app_name + " -> " + dep_name);
                return false;
            }
            
            // If not visited, visit it
            if (visited_.find(dep_name) == visited_.end()) {
                if (!topological_sort(dep_name)) {
                    return false;
                }
            }
        }
        
        // Remove from current stack and add to install order
        in_stack_.erase(app_name);
        install_order_.push_back(app_name);
        
        return true;
    }
    
    std::string extract_app_name(const std::string& dependency) {
        // Handle different dependency formats:
        // - "app_name"
        // - "bucket/app_name"
        // - "app_name@version"
        // - "bucket/app_name@version"
        
        std::string result = dependency;
        
        // Remove version specifier if present
        size_t at_pos = result.find('@');
        if (at_pos != std::string::npos) {
            result = result.substr(0, at_pos);
        }
        
        // Extract app name from bucket/app format
        size_t slash_pos = result.find('/');
        if (slash_pos != std::string::npos) {
            result = result.substr(slash_pos + 1);
        }
        
        return result;
    }
    
public:
    // Utility function to check if an app is already installed
    static bool is_app_installed(const std::string& app_name) {
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        auto app_dir = apps_dir / app_name;
        
        return std::filesystem::exists(app_dir) && std::filesystem::is_directory(app_dir);
    }
    
    // Filter out already installed apps from the install order
    static std::vector<std::string> filter_installed_apps(const std::vector<std::string>& apps) {
        std::vector<std::string> result;
        
        for (const auto& app : apps) {
            if (!is_app_installed(app)) {
                result.push_back(app);
            } else {
                SPDLOG_INFO("App {} is already installed, skipping", app);
            }
        }
        
        return result;
    }
    
    // Get detailed dependency information
    static std::vector<DependencyNode> get_dependency_info(const std::vector<std::string>& app_names) {
        DependencyResolver resolver;
        std::vector<DependencyNode> result;
        
        for (const auto& app_name : app_names) {
            if (resolver.build_dependency_graph(app_name)) {
                auto it = resolver.nodes_.find(app_name);
                if (it != resolver.nodes_.end()) {
                    result.push_back(it->second);
                }
            }
        }
        
        return result;
    }
    
    // Validate that all dependencies can be resolved
    static bool validate_dependencies(const std::vector<std::string>& app_names, 
                                    std::vector<std::string>& missing_deps) {
        auto result = resolve(app_names);
        missing_deps = result.missing_dependencies;
        return result.success && result.missing_dependencies.empty();
    }
    
    // Get the full dependency tree for display purposes
    static std::unordered_map<std::string, std::vector<std::string>> get_dependency_tree(
        const std::vector<std::string>& app_names) {
        
        DependencyResolver resolver;
        std::unordered_map<std::string, std::vector<std::string>> tree;
        
        for (const auto& app_name : app_names) {
            if (resolver.build_dependency_graph(app_name)) {
                auto it = resolver.nodes_.find(app_name);
                if (it != resolver.nodes_.end()) {
                    std::vector<std::string> deps;
                    for (const auto& dep : it->second.dependencies) {
                        deps.push_back(resolver.extract_app_name(dep));
                    }
                    tree[app_name] = deps;
                }
            }
        }
        
        return tree;
    }
};

} // namespace sco
