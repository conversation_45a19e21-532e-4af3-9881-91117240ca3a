[{"directory": "C:/Code/scoop/Build/msvc-debug", "command": "C:\\PROGRA~2\\MICROS~2\\2019\\PROFES~1\\VC\\Tools\\MSVC\\1429~1.301\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DCLI11_COMPILE -DFMT_SHARED -DSPDLOG_FMT_EXTERNAL -DSPDLOG_FWRITE_UNLOCKED -IC:\\Code\\scoop\\src -IC:\\Code\\scoop\\Build\\msvc-debug -external:IC:\\Local\\vcpkg-latest\\installed\\x64-windows\\include -external:W0 /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /Zc:__cplusplus /utf-8 /FoCMakeFiles\\sco.dir\\src\\main.cpp.obj /FdCMakeFiles\\sco.dir\\ /FS -c C:\\Code\\scoop\\src\\main.cpp", "file": "C:\\Code\\scoop\\src\\main.cpp", "output": "CMakeFiles\\sco.dir\\src\\main.cpp.obj"}]