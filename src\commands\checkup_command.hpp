#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include <iostream>
#include <filesystem>
#include <spdlog/spdlog.h>
#include <windows.h>

namespace sco {

class CheckupCommand : public BaseCommand {
public:
    CheckupCommand() = default;
    
    int execute() override {
        SPDLOG_INFO("Checking system for potential problems...");
        SPDLOG_INFO("");

        int issues = 0;
        issues += check_scoop_directories();
        issues += check_environment_variables();
        issues += check_permissions();
        issues += check_powershell_execution_policy();
        issues += check_long_path_support();

        SPDLOG_INFO("");
        if (issues == 0) {
            SPDLOG_INFO("✓ No issues found.");
        } else {
            SPDLOG_WARN("⚠ Found {} potential issue(s).", issues);
        }

        return issues > 0 ? 1 : 0;
    }
    
    std::string get_name() const override { return "checkup"; }
    std::string get_description() const override { return "Check for potential problems"; }
    
private:
    int check_scoop_directories() {
        SPDLOG_INFO("Checking Scoop directories...");
        int issues = 0;

        auto& config = Config::instance();
        config.load();

        auto root_path = config.get_root_path();
        auto config_file = config.get_config_file();
        auto apps_dir = config.get_apps_dir();
        auto cache_dir = config.get_cache_dir();
        auto buckets_dir = config.get_buckets_dir();
        auto shims_dir = config.get_shims_dir();

        // Check configuration file
        if (!std::filesystem::exists(config_file)) {
            SPDLOG_WARN("  ⚠ Configuration file does not exist: {}", config_file.string());
            SPDLOG_INFO("    Will be created with default settings on first use.");
        } else {
            SPDLOG_INFO("  ✓ Configuration file exists: {}", config_file.string());
        }

        // Check if main scoop directory exists
        if (!std::filesystem::exists(root_path)) {
            SPDLOG_ERROR("  ✗ Scoop root directory does not exist: {}", root_path.string());
            SPDLOG_INFO("    Run 'sco install <app>' to initialize Scoop.");
            issues++;
        } else {
            SPDLOG_INFO("  ✓ Scoop root directory exists: {}", root_path.string());
        }

        // Check subdirectories
        std::vector<std::pair<std::filesystem::path, std::string>> dirs = {
            {apps_dir, "Apps directory"},
            {cache_dir, "Cache directory"},
            {buckets_dir, "Buckets directory"},
            {shims_dir, "Shims directory"}
        };

        for (const auto& [dir, name] : dirs) {
            if (std::filesystem::exists(root_path) && !std::filesystem::exists(dir)) {
                SPDLOG_WARN("  ⚠ {} missing: {}", name, dir.string());
                try {
                    std::filesystem::create_directories(dir);
                    SPDLOG_INFO("    Created directory.");
                } catch (const std::exception& e) {
                    SPDLOG_ERROR("    Failed to create directory: {}", e.what());
                    issues++;
                }
            }
        }

        return issues;
    }
    
    int check_environment_variables() {
        SPDLOG_INFO("");
        SPDLOG_INFO("Checking environment variables...");
        int issues = 0;

        auto& config = Config::instance();
        auto shims_dir = config.get_shims_dir();

        // Check if shims directory is in PATH
        char* path_env = nullptr;
        size_t len = 0;
        if (_dupenv_s(&path_env, &len, "PATH") == 0 && path_env != nullptr) {
            std::string path_str(path_env);
            free(path_env);

            if (path_str.find(shims_dir.string()) == std::string::npos) {
                SPDLOG_WARN("  ⚠ Shims directory not in PATH: {}", shims_dir.string());
                SPDLOG_INFO("    Add this directory to your PATH environment variable.");
                issues++;
            } else {
                SPDLOG_INFO("  ✓ Shims directory is in PATH");
            }
        } else {
            SPDLOG_ERROR("  ✗ Could not read PATH environment variable");
            issues++;
        }

        return issues;
    }
    
    int check_permissions() {
        SPDLOG_INFO("");
        SPDLOG_INFO("Checking permissions...");
        int issues = 0;

        auto& config = Config::instance();
        auto root_path = config.get_root_path();

        if (std::filesystem::exists(root_path)) {
            // Try to create a test file
            auto test_file = root_path / "test_permissions.tmp";
            try {
                std::ofstream file(test_file);
                if (file.is_open()) {
                    file << "test";
                    file.close();
                    std::filesystem::remove(test_file);
                    SPDLOG_INFO("  ✓ Write permissions OK");
                } else {
                    SPDLOG_ERROR("  ✗ No write permissions to Scoop directory");
                    issues++;
                }
            } catch (const std::exception& e) {
                SPDLOG_ERROR("  ✗ Permission check failed: {}", e.what());
                issues++;
            }
        }

        return issues;
    }
    
    int check_powershell_execution_policy() {
        SPDLOG_INFO("");
        SPDLOG_INFO("Checking PowerShell execution policy...");
        int issues = 0;

        // This is a placeholder - in a real implementation, we would check
        // PowerShell execution policy using Windows APIs or by running PowerShell
        SPDLOG_INFO("  ℹ PowerShell execution policy check not implemented");
        SPDLOG_INFO("    Ensure execution policy allows running scripts:");
        SPDLOG_INFO("    Set-ExecutionPolicy RemoteSigned -Scope CurrentUser");

        return issues;
    }

    int check_long_path_support() {
        SPDLOG_INFO("");
        SPDLOG_INFO("Checking long path support...");
        int issues = 0;

        // Check Windows version and long path support
        SPDLOG_INFO("  ℹ Long path support check not fully implemented");
        SPDLOG_INFO("    For Windows 10 version 1607+, enable long path support:");
        SPDLOG_INFO("    Computer Configuration > Administrative Templates > System > Filesystem");
        SPDLOG_INFO("    Enable 'Enable Win32 long paths'");

        return issues;
    }
};

} // namespace sco
