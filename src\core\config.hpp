#pragma once

#include <string>
#include <unordered_map>
#include <filesystem>
#include <fstream>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <windows.h>

namespace sco {

class Config {
public:
    static Config& instance() {
        static Config instance;
        return instance;
    }
    
    // Configuration management
    void load() {
        auto config_file = get_config_file();

        SPDLOG_DEBUG("Loading configuration from: {}", config_file.string());

        if (!std::filesystem::exists(config_file)) {
            SPDLOG_DEBUG("Configuration file not found, creating with defaults");
            set_defaults();
            save();
            return;
        }

        try {
            std::ifstream file(config_file);
            if (file.is_open()) {
                file >> config_data_;
                SPDLOG_DEBUG("Configuration loaded successfully");

                // 确保 root_path 存在，如果不存在则设置默认值
                if (!config_data_.contains("root_path") || config_data_["root_path"].get<std::string>().empty()) {
                    std::filesystem::path default_root = global_mode_ ?
                        (get_program_data() / "scoop") :
                        (get_user_profile() / "scoop");
                    config_data_["root_path"] = default_root.string();
                    save(); // 保存更新后的配置
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_WARN("Failed to load configuration: {}", e.what());
            set_defaults();
        }
    }
    
    void save() {
        auto config_file = get_config_file();
        
        try {
            // Ensure directory exists
            std::filesystem::create_directories(config_file.parent_path());
            
            std::ofstream file(config_file);
            if (file.is_open()) {
                file << config_data_.dump(2);
                spdlog::debug("Configuration saved to: {}", config_file.string());
            }
        } catch (const std::exception& e) {
            spdlog::error("Failed to save configuration: {}", e.what());
        }
    }
    
    // Getters
    std::string get(const std::string& key, const std::string& default_value = "") const {
        if (config_data_.contains(key)) {
            return config_data_[key].get<std::string>();
        }
        return default_value;
    }
    
    bool get_bool(const std::string& key, bool default_value = false) const {
        if (config_data_.contains(key)) {
            return config_data_[key].get<bool>();
        }
        return default_value;
    }
    
    int get_int(const std::string& key, int default_value = 0) const {
        if (config_data_.contains(key)) {
            return config_data_[key].get<int>();
        }
        return default_value;
    }
    
    // Setters
    void set(const std::string& key, const std::string& value) {
        config_data_[key] = value;
    }
    
    void set_bool(const std::string& key, bool value) {
        config_data_[key] = value;
    }
    
    void set_int(const std::string& key, int value) {
        config_data_[key] = value;
    }
    
    // Global mode
    void set_global_mode(bool global) { global_mode_ = global; }
    bool is_global_mode() const { return global_mode_; }
    
    // Paths
    std::filesystem::path get_scoop_dir() const {
        // 首先尝试从配置文件读取 root_path
        std::string root_path = get("root_path");
        if (!root_path.empty()) {
            return std::filesystem::path(root_path);
        }

        // 如果没有配置 root_path，使用默认路径
        if (global_mode_) {
            return get_program_data() / "scoop";
        } else {
            return get_user_profile() / "scoop";
        }
    }
    
    std::filesystem::path get_apps_dir() const {
        return get_scoop_dir() / "apps";
    }
    
    std::filesystem::path get_cache_dir() const {
        return get_scoop_dir() / "cache";
    }
    
    std::filesystem::path get_buckets_dir() const {
        return get_scoop_dir() / "buckets";
    }
    
    std::filesystem::path get_shims_dir() const {
        return get_scoop_dir() / "shims";
    }
    
    std::filesystem::path get_config_file() const {
        // Scoop 配置文件位于 %USERPROFILE%\.config\scoop\config.json
        return get_user_profile() / ".config" / "scoop" / "config.json";
    }
    
    // Get the actual root path for Scoop installation
    std::filesystem::path get_root_path() const {
        std::string root_path = get("root_path");
        if (!root_path.empty()) {
            return std::filesystem::path(root_path);
        }

        // 默认路径
        if (global_mode_) {
            return get_program_data() / "scoop";
        } else {
            return get_user_profile() / "scoop";
        }
    }

    // Default configuration values
    void set_defaults() {
        // 设置默认的 root_path
        std::filesystem::path default_root = global_mode_ ?
            (get_program_data() / "scoop") :
            (get_user_profile() / "scoop");

        config_data_ = nlohmann::json{
            {"root_path", default_root.string()},
            {"aria2-enabled", true},
            {"aria2-warning-enabled", true},
            {"aria2-retry-wait", 2},
            {"aria2-split", 5},
            {"aria2-max-connection-per-server", 5},
            {"aria2-min-split-size", "5M"},
            {"aria2-options", ""},
            {"debug", false},
            {"force_update", false},
            {"show_update_log", true},
            {"scoop_repo", "https://github.com/ScoopInstaller/Scoop"},
            {"scoop_branch", "master"}
        };
    }

private:
    Config() = default;
    ~Config() = default;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;
    
    nlohmann::json config_data_;
    bool global_mode_ = false;
    
    std::filesystem::path get_user_profile() const {
        char* userprofile = nullptr;
        size_t len = 0;
        if (_dupenv_s(&userprofile, &len, "USERPROFILE") == 0 && userprofile != nullptr) {
            std::filesystem::path path(userprofile);
            free(userprofile);
            return path;
        }
        return std::filesystem::current_path();
    }
    
    std::filesystem::path get_program_data() const {
        char* programdata = nullptr;
        size_t len = 0;
        if (_dupenv_s(&programdata, &len, "PROGRAMDATA") == 0 && programdata != nullptr) {
            std::filesystem::path path(programdata);
            free(programdata);
            return path;
        }
        return std::filesystem::path("C:\\ProgramData");
    }
};

} // namespace sco
