# Scoop 配置文件集成总结

## ✅ 已完成的集成工作

### 1. 配置文件路径修正
**之前**: `{scoop_dir}/config.json`
**现在**: `%USERPROFILE%\.config\scoop\config.json`

这符合 Scoop 的实际配置文件位置标准。

### 2. root_path 支持
添加了对 `root_path` 配置项的完整支持：

```cpp
// 获取配置的根路径
std::filesystem::path root_path = config.get_root_path();

// 如果配置文件中有 root_path，使用它
// 否则使用默认路径 (%USERPROFILE%\scoop 或 %PROGRAMDATA%\scoop)
```

### 3. 配置文件结构
支持标准的 Scoop 配置格式：
```json
{
  "root_path": "C:\\Users\\<USER>\\scoop",
  "aria2-enabled": true,
  "aria2-warning-enabled": true,
  "aria2-retry-wait": 2,
  "aria2-split": 5,
  "aria2-max-connection-per-server": 5,
  "aria2-min-split-size": "5M",
  "aria2-options": "",
  "debug": false,
  "force_update": false,
  "show_update_log": true,
  "scoop_repo": "https://github.com/ScoopInstaller/Scoop",
  "scoop_branch": "master"
}
```

## 🔧 修改的文件

### src/core/config.hpp
- ✅ 修改 `get_config_file()` 返回正确路径
- ✅ 添加 `get_root_path()` 方法
- ✅ 修改 `get_scoop_dir()` 使用配置的 root_path
- ✅ 更新 `set_defaults()` 包含 root_path
- ✅ 增强 `load()` 方法处理 root_path

### src/commands/config_command.hpp
- ✅ 显示 root_path 配置项
- ✅ 支持查询和设置 root_path
- ✅ 添加 root_path 路径验证
- ✅ 更新路径显示包含实际根路径

### src/commands/checkup_command.hpp
- ✅ 检查配置文件存在性
- ✅ 使用 root_path 进行目录检查
- ✅ 验证根目录权限

## 🎯 功能特性

### 自动配置管理
```cpp
// 自动加载配置
auto& config = Config::instance();
config.load();

// 如果配置文件不存在，自动创建默认配置
// 如果 root_path 未设置，使用默认路径
```

### 路径解析
```cpp
// 所有路径都基于配置的 root_path
auto apps_dir = config.get_apps_dir();      // {root_path}/apps
auto cache_dir = config.get_cache_dir();    // {root_path}/cache
auto buckets_dir = config.get_buckets_dir(); // {root_path}/buckets
auto shims_dir = config.get_shims_dir();    // {root_path}/shims
```

### 配置验证
```cpp
// root_path 设置时的验证
- 必须是绝对路径
- 自动创建目录（如果不存在）
- 验证写入权限
```

## 📋 命令行为变化

### sco config
```bash
# 显示所有配置，包括 root_path
sco config

# 查看当前 root_path
sco config root_path

# 设置新的 root_path
sco config root_path "D:\MyScoop"
```

### sco list
```bash
# 现在从配置的 root_path 读取应用
sco list
# 扫描: {root_path}/apps/ 目录
```

### sco checkup
```bash
# 检查配置文件和根目录
sco checkup
# 验证: %USERPROFILE%\.config\scoop\config.json
# 验证: {root_path}/ 目录结构
```

### sco status
```bash
# 显示基于配置 root_path 的状态
sco status
# 检查: {root_path}/ 下的各种状态
```

## 🔄 兼容性保证

### 与原版 Scoop 兼容
- ✅ 使用相同的配置文件位置
- ✅ 使用相同的配置格式
- ✅ 支持相同的配置项
- ✅ 兼容现有的目录结构

### 现有用户迁移
- ✅ 自动检测现有配置文件
- ✅ 读取现有 root_path 设置
- ✅ 无缝使用现有安装

### 新用户体验
- ✅ 自动创建默认配置
- ✅ 智能路径选择
- ✅ 自动目录创建

## 🧪 测试场景

### 场景1: 新用户
```bash
# 1. 首次运行
sco config
# 结果: 自动创建 %USERPROFILE%\.config\scoop\config.json
#       设置 root_path 为 %USERPROFILE%\scoop

# 2. 查看配置
sco config root_path
# 输出: C:\Users\<USER>\scoop
```

### 场景2: 现有 Scoop 用户
```bash
# 1. 已有配置文件存在
sco list
# 结果: 读取现有配置，显示已安装应用

# 2. 查看当前配置
sco config
# 结果: 显示现有的 root_path 和其他配置
```

### 场景3: 自定义路径
```bash
# 1. 设置自定义路径
sco config root_path "D:\MyApps\scoop"
# 结果: 验证路径，创建目录，保存配置

# 2. 验证生效
sco list
# 结果: 从新路径 D:\MyApps\scoop\apps 读取应用
```

## 🐛 错误处理

### 配置文件问题
- 文件损坏: 重置为默认配置
- 权限问题: 提供修复建议
- 路径无效: 验证并提示错误

### 目录问题
- 目录不存在: 自动创建
- 权限不足: 检测并提示
- 磁盘空间: 验证可用空间

## 📊 调试支持

使用 `--verbose` 查看详细过程：
```bash
sco --verbose config
# 显示:
# - 配置文件加载过程
# - root_path 解析过程
# - 目录检查结果
# - 默认值设置过程
```

## 🚀 下一步

这个配置集成为后续功能奠定了基础：

1. **install 命令** - 将使用配置的 root_path 安装应用
2. **uninstall 命令** - 从配置的路径卸载应用
3. **update 命令** - 更新配置路径下的应用
4. **search 命令** - 搜索可安装的应用

所有命令现在都能正确读取和使用 Scoop 的标准配置文件！
