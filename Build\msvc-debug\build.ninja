# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sco
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Code\scoop\Build\msvc-debug\
# =============================================================================
# Object build statements for EXECUTABLE target sco


#############################################
# Order-only phony target for sco

build cmake_object_order_depends_target_sco: phony || .

build CMakeFiles\sco.dir\src\main.cpp.obj: CXX_COMPILER__sco_unscanned_Debug C$:\Code\scoop\src\main.cpp || cmake_object_order_depends_target_sco
  DEFINES = -DCLI11_COMPILE -DFMT_SHARED -DSPDLOG_FMT_EXTERNAL -DSPDLOG_FWRITE_UNLOCKED
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /Zc:__cplusplus /utf-8
  INCLUDES = -IC:\Code\scoop\src -IC:\Code\scoop\Build\msvc-debug -external:IC:\Local\vcpkg-latest\installed\x64-windows\include -external:W0
  OBJECT_DIR = CMakeFiles\sco.dir
  OBJECT_FILE_DIR = CMakeFiles\sco.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\sco.dir\
  TARGET_PDB = sco.pdb


# =============================================================================
# Link build statements for EXECUTABLE target sco


#############################################
# Link the executable sco.exe

build sco.exe: CXX_EXECUTABLE_LINKER__sco_Debug CMakeFiles\sco.dir\src\main.cpp.obj | C$:\Local\vcpkg-latest\installed\x64-windows\debug\lib\CLI11.lib C$:\Local\vcpkg-latest\installed\x64-windows\debug\lib\fmtd.lib
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = C:\Local\vcpkg-latest\installed\x64-windows\debug\lib\CLI11.lib  C:\Local\vcpkg-latest\installed\x64-windows\debug\lib\fmtd.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\sco.dir
  POST_BUILD = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Code\scoop\Build\msvc-debug && C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Local/vcpkg-latest/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Code/scoop/Build/msvc-debug/sco.exe -installedDir C:/Local/vcpkg-latest/installed/x64-windows/debug/bin -OutVariable out"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\sco.dir\
  TARGET_FILE = sco.exe
  TARGET_IMPLIB = sco.lib
  TARGET_PDB = sco.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Code\scoop\Build\msvc-debug && C:\Users\<USER>\OneDrive\app\cmake\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Code\scoop\Build\msvc-debug && C:\Users\<USER>\OneDrive\app\cmake\bin\cmake.exe --regenerate-during-build -SC:\Code\scoop -BC:\Code\scoop\Build\msvc-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles\install.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Code\scoop\Build\msvc-debug && C:\Users\<USER>\OneDrive\app\cmake\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles\install.util


#############################################
# Utility command for install/local

build CMakeFiles\install\local.util: CUSTOM_COMMAND all
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Code\scoop\Build\msvc-debug && C:\Users\<USER>\OneDrive\app\cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install\local: phony CMakeFiles\install\local.util

# =============================================================================
# Target aliases.

build sco: phony sco.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Code/scoop/Build/msvc-debug

build all: phony sco.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Code\scoop\CMakeLists.txt C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11Config-debug.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11Config-release.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11Config.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11ConfigVersion.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-config-version.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-config.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-targets-debug.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-targets-release.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-targets.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfig.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake C$:\Local\vcpkg-latest\scripts\buildsystems\vcpkg.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeDependentOption.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CheckLibraryExists.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\FindThreads.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\GNUInstallDirs.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.1\CMakeCXXCompiler.cmake CMakeFiles\3.30.1\CMakeRCCompiler.cmake CMakeFiles\3.30.1\CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Code\scoop\CMakeLists.txt C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11Config-debug.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11Config-release.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11Config.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\cli11\CLI11ConfigVersion.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-config-version.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-config.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-targets-debug.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-targets-release.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\fmt\fmt-targets.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfig.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake C$:\Local\vcpkg-latest\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake C$:\Local\vcpkg-latest\scripts\buildsystems\vcpkg.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeDependentOption.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\CheckLibraryExists.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\FindThreads.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\GNUInstallDirs.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Users\kizi\OneDrive\app\cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.1\CMakeCXXCompiler.cmake CMakeFiles\3.30.1\CMakeRCCompiler.cmake CMakeFiles\3.30.1\CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
